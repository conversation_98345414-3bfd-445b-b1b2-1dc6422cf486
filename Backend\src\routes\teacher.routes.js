import express from 'express';
import {
    createTeacher,
    getAllTeachers,
    getTeacher<PERSON>yId,
    updateTeacher,
    deleteTeacher
} from '../controllers/teacher.controller.js';
import { authenticateToken } from '../middlewares/auth.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

router.post('/', createTeacher);
router.get('/', getAllTeachers);
router.get('/:id', getTeacherById);
router.put('/:id', updateTeacher);
router.delete('/:id', deleteTeacher);

export default router;
