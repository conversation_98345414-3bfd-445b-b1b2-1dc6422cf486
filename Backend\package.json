{"name": "pagination", "version": "1.0.0", "main": "src/index.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon src/index.js", "seed": "node src/seed.js"}, "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^5.1.0", "mysql2": "^3.14.1", "sequelize": "^6.37.7", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@faker-js/faker": "^9.8.0"}}