import jwt from 'jsonwebtoken';

export const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader?.split(' ')[1];

    if(!token) {
        return res.status(401).json({error: 'Token missing'});

    }
    try {
        const user = jwt.verify(token, process.env.JWT_SECRET);
        req.user = user;
        next();

    }catch(err){
        res.status(403).json ({ error : 'Invalid or expired token'});
    }
}